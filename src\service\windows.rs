use crate::ServiceParams;

#[cfg(target_os = "windows")]
pub async fn install_service(_service_params: ServiceParams) -> anyhow::Result<()> {


    // Install the service first
    service::install()?;

    // Then try to run it (this would typically be done by the service manager)
    let res = service::run();
    println!("Service run result: {:?}", res);

    Ok(())
}

#[cfg(windows)]
mod service {
    use std::{ffi::OsString, path::Path, sync::mpsc, thread::sleep, time::Duration};

    use anyhow::Context as _;
    use windows::{
        core::{PWSTR, PCWSTR},
        Win32::Foundation::{HANDLE, INVALID_HANDLE_VALUE, CloseHandle, LocalFree},
        Win32::Security::{
            DACL_SECURITY_INFORMATION, PSID, PSECURITY_DESCRIPTOR,
        },
        Win32::Security::Authorization::{
            SetNamedSecurityInfoW, SE_FILE_OBJECT, SE_OBJECT_TYPE,
        },
        Win32::Storage::FileSystem::{
            CreateFileW, FILE_ACCESS_RIGHTS, FILE_ATTRIBUTE_DIRECTORY,
            FILE_FLAG_BACKUP_SEMANTICS, OPEN_EXISTING,
        },
    };
    use windows_service::{
        define_windows_service,
        service::{
            ServiceAccess, ServiceControl, ServiceControlAccept, ServiceErrorControl, ServiceExitCode, ServiceInfo, ServiceStartType, ServiceState, ServiceStatus, ServiceType
        },
        service_control_handler::{self, ServiceControlHandlerResult},
        service_dispatcher, service_manager::{ServiceManager, ServiceManagerAccess},
    };



    const SERVICE_NAME: &str = "iroh-ssh";
    const SERVICE_TYPE: ServiceType = ServiceType::OWN_PROCESS;
    const SERVICE_DISPLAY_NAME: &str = "iroh-ssh service";
    const SERVICE_DESCRIPTION: &str = "ssh without ip";
    const SERVICE_ACCOUNT_NAME: &str = "NT SERVICE\\iroh-ssh";

    pub fn install() -> anyhow::Result<()> {

        let profile_ssh_dir = Path::new("C:\\Windows\\ServiceProfiles\\")
            .join(SERVICE_NAME)
            .join(".ssh");

        println!("Creating service data directory: {:?}", &profile_ssh_dir);
        std::fs::create_dir_all(&profile_ssh_dir)
            .with_context(|| format!("Failed to create service directory at {:?}", &profile_ssh_dir))?;

        println!("Setting permissions for '{}' on {:?}", SERVICE_ACCOUNT_NAME, &profile_ssh_dir);

        // Set file system permissions for the SSH directory
        set_ssh_directory_permissions(&profile_ssh_dir, SERVICE_ACCOUNT_NAME)
            .with_context(|| format!("Failed to set permissions on {:?}", &profile_ssh_dir))?;


        // Print installation summary
        print_installation_summary(SERVICE_ACCOUNT_NAME, &profile_ssh_dir)?;


        let manager_access = ServiceManagerAccess::CONNECT | ServiceManagerAccess::CREATE_SERVICE;
        let service_manager = ServiceManager::local_computer(None::<&str>, manager_access)?;
        let service_binary_path = ::std::env::current_exe()?
            .with_file_name("iroh-ssh.exe");

        let service_info = ServiceInfo {
            name: OsString::from(SERVICE_NAME),
            display_name: OsString::from(SERVICE_DISPLAY_NAME),
            service_type: ServiceType::OWN_PROCESS,
            start_type: ServiceStartType::AutoStart,
            error_control: ServiceErrorControl::Normal,
            executable_path: service_binary_path,
            launch_arguments: vec![OsString::from("server"), OsString::from("--ssh-port"), OsString::from("22"), OsString::from("--persist")],
            dependencies: vec![],
            account_name: Some(OsString::from(SERVICE_ACCOUNT_NAME)),
            account_password: None,
        };
        let service =
            service_manager.create_service(&service_info, ServiceAccess::CHANGE_CONFIG)?;
        service.set_description("ssh without ip (requires ssh server)")?;
        Ok(())
    }

    pub fn run() -> windows_service::Result<()> {
        // Register generated `ffi_service_main` with the system and start the service, blocking
        // this thread until the service is stopped.
        service_dispatcher::start(SERVICE_NAME, ffi_service_main)
    }

    // Generate the windows service boilerplate.
    // The boilerplate contains the low-level service entry function (ffi_service_main) that parses
    // incoming service arguments into Vec<OsString> and passes them to user defined service
    // entry (my_service_main).
    define_windows_service!(ffi_service_main, my_service_main);

    // Service entry function which is called on background thread by the system with service
    // parameters. There is no stdout or stderr at this point so make sure to configure the log
    // output to file if needed.
    pub fn my_service_main(_arguments: Vec<OsString>) {
        if let Err(_e) = run_service() {
            // Handle the error, by logging or something.
        }
    }

    pub fn run_service() -> windows_service::Result<()> {
        // Create a channel to be able to poll a stop event from the service worker loop.
        let (shutdown_tx, shutdown_rx) = mpsc::channel();

        // Define system service event handler that will be receiving service events.
        let event_handler = move |control_event| -> ServiceControlHandlerResult {
            match control_event {
                // Notifies a service to report its current status information to the service
                // control manager. Always return NoError even if not implemented.
                ServiceControl::Interrogate => ServiceControlHandlerResult::NoError,

                // Handle stop
                ServiceControl::Stop => {
                    shutdown_tx.send(()).unwrap();
                    ServiceControlHandlerResult::NoError
                }

                // treat the UserEvent as a stop request
                ServiceControl::UserEvent(code) => {
                    if code.to_raw() == 130 {
                        shutdown_tx.send(()).unwrap();
                    }
                    ServiceControlHandlerResult::NoError
                }

                _ => ServiceControlHandlerResult::NotImplemented,
            }
        };

        // Register system service event handler.
        // The returned status handle should be used to report service status changes to the system.
        let status_handle = service_control_handler::register(SERVICE_NAME, event_handler)?;

        // Tell the system that service is running
        status_handle.set_service_status(ServiceStatus {
            service_type: SERVICE_TYPE,
            current_state: ServiceState::Running,
            controls_accepted: ServiceControlAccept::STOP,
            exit_code: ServiceExitCode::Win32(0),
            checkpoint: 0,
            wait_hint: Duration::default(),
            process_id: None,
        })?;

        println!("in service");
        sleep(Duration::from_secs(10));
        // Poll shutdown event.
        let _ = shutdown_rx.recv();
        println!("Shutting down...");

        // Tell the system that service has stopped.
        status_handle.set_service_status(ServiceStatus {
            service_type: SERVICE_TYPE,
            current_state: ServiceState::Stopped,
            controls_accepted: ServiceControlAccept::empty(),
            exit_code: ServiceExitCode::Win32(0),
            checkpoint: 0,
            wait_hint: Duration::default(),
            process_id: None,
        })?;

        Ok(())
    }

    /// Set file system permissions for the SSH directory using Windows API directly
    fn set_ssh_directory_permissions(ssh_dir: &Path, service_account: &str) -> anyhow::Result<()> {
        println!("Setting file permissions for service account: {}", service_account);

        // Try the direct Windows API approach first
        match set_permissions_via_win32_api(ssh_dir, service_account) {
            Ok(()) => {
                println!("✅ File permissions set successfully using Windows API");
                return Ok(());
            }
            Err(e) => {
                println!("⚠️  Windows API approach failed: {}", e);
                println!("Falling back to PowerShell...");
            }
        }

        // Fallback to PowerShell approach
        match apply_file_permissions_powershell(ssh_dir, service_account) {
            Ok(()) => {
                println!("✅ File permissions configured successfully via PowerShell");
            }
            Err(e) => {
                println!("⚠️  PowerShell approach also failed: {}", e);
                println!("\n📋 MANUAL PERMISSION SETUP REQUIRED:");
                println!("1. Run this installer as Administrator");
                println!("2. The service account '{}' needs full control over: {:?}", service_account, ssh_dir);
                println!("3. You can set this manually with:");
                println!("   icacls \"{}\" /grant \"{}:(OI)(CI)F\" /T", ssh_dir.display(), service_account);
            }
        }

        Ok(())
    }

    /// Set permissions using Windows API directly
    fn set_permissions_via_win32_api(ssh_dir: &Path, service_account: &str) -> anyhow::Result<()> {
        println!("Attempting to set permissions using Windows API...");

        // Convert the path to a wide string for Windows API
        let path_wide: Vec<u16> = ssh_dir.as_os_str()
            .encode_wide()
            .chain(std::iter::once(0))
            .collect();

        // Create SDDL string that grants full control to the service account
        // Format: D:(A;OICI;FA;;;SID)(A;OICI;FA;;;BA)
        // - D: = DACL
        // - A = Allow ACE
        // - OICI = Object Inherit + Container Inherit
        // - FA = Full Access
        // - SID = Service account, BA = Built-in Administrators
        let sddl_string = format!(
            "D:(A;OICI;FA;;;{})(A;OICI;FA;;;BA)",
            service_account.trim_start_matches("NT SERVICE\\")
        );

        println!("Using SDDL: {}", sddl_string);

        // Convert SDDL to wide string
        let sddl_wide: Vec<u16> = sddl_string
            .encode_utf16()
            .chain(std::iter::once(0))
            .collect();

        unsafe {
            // Convert SDDL string to security descriptor
            let mut security_descriptor: PSECURITY_DESCRIPTOR = std::ptr::null_mut();
            let mut sd_size: u32 = 0;

            let result = ConvertStringSecurityDescriptorToSecurityDescriptorW(
                PCWSTR(sddl_wide.as_ptr()),
                1, // SDDL_REVISION_1
                &mut security_descriptor,
                &mut sd_size,
            );

            if result.is_err() {
                anyhow::bail!("Failed to convert SDDL to security descriptor: {:?}", result);
            }

            // Apply the security descriptor to the directory
            let result = SetNamedSecurityInfoW(
                PCWSTR(path_wide.as_ptr()),
                SE_FILE_OBJECT,
                DACL_SECURITY_INFORMATION,
                PSID::default(),        // Owner
                PSID::default(),        // Group
                security_descriptor,    // DACL
                std::ptr::null_mut(),   // SACL
            );

            // Clean up the security descriptor
            if !security_descriptor.is_null() {
                LocalFree(security_descriptor as *mut _);
            }

            if result != 0 {
                anyhow::bail!("SetNamedSecurityInfoW failed with error: {}", result);
            }
        }

        println!("✅ Permissions set successfully using Windows API");
        Ok(())
    }

    /// Generate a hash for the service account (simplified approach)
    fn get_service_account_hash(service_name: &str) -> String {
        // This is a simplified hash - in reality, Windows uses a more complex algorithm
        // For production, you might want to use the actual Windows API to get the real SID
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        service_name.hash(&mut hasher);
        let hash = hasher.finish();

        // Format as a series of sub-authorities (simplified)
        format!("{}-{}-{}-{}",
            (hash >> 48) & 0xFFFF,
            (hash >> 32) & 0xFFFF,
            (hash >> 16) & 0xFFFF,
            hash & 0xFFFF
        )
    }

    /// Apply file permissions using PowerShell as a fallback
    fn apply_file_permissions_powershell(ssh_dir: &Path, service_account: &str) -> anyhow::Result<()> {
        println!("Attempting to apply file permissions using PowerShell...");

        let powershell_script = format!(
            r#"
            try {{
                $path = '{}'
                $account = '{}'

                # Get current ACL
                $acl = Get-Acl -Path $path

                # Create new access rule for full control
                $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule(
                    $account,
                    'FullControl',
                    'ContainerInherit,ObjectInherit',
                    'None',
                    'Allow'
                )

                # Add the rule to ACL
                $acl.SetAccessRule($accessRule)

                # Apply the ACL
                Set-Acl -Path $path -AclObject $acl

                Write-Host "✅ Permissions applied successfully"
                exit 0
            }} catch {{
                Write-Host "❌ Failed to apply permissions: $_"
                exit 1
            }}
            "#,
            ssh_dir.display().to_string().replace('\\', "\\\\"),
            service_account
        );

        let output = std::process::Command::new("powershell")
            .arg("-ExecutionPolicy")
            .arg("Bypass")
            .arg("-Command")
            .arg(&powershell_script)
            .output()
            .context("Failed to execute PowerShell command")?;

        if output.status.success() {
            println!("✅ File permissions applied successfully via PowerShell");
            Ok(())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            println!("⚠️  PowerShell permission setting failed: {}", stderr);
            println!("💡 Manual permission setting required - see instructions above");
            Ok(()) // Don't fail the installation, just warn
        }
    }

    /// Print a comprehensive installation summary
    fn print_installation_summary(service_account: &str, ssh_dir: &Path) -> anyhow::Result<()> {
        let separator = "=".repeat(60);
        println!("\n{}", separator);
        println!("🚀 IROH-SSH WINDOWS SERVICE INSTALLATION SUMMARY");
        println!("{}", separator);

        println!("\n📋 Service Configuration:");
        println!("  Service Name: {}", SERVICE_NAME);
        println!("  Display Name: {}", SERVICE_DISPLAY_NAME);
        println!("  Account: {}", service_account);
        println!("  SSH Directory: {:?}", ssh_dir);

        println!("\n🔐 Security Configuration:");
        println!("  ✓ Virtual Service Account created");
        println!("  ✓ Isolated service profile directory");
        println!("  ✓ Network service privileges");
        println!("  ✓ Log on as service right (automatic)");

        println!("\n🌐 Network Capabilities:");
        println!("  ✓ Can bind to local TCP ports");
        println!("  ✓ Can make outbound QUIC/UDP connections");
        println!("  ✓ Can access network resources as computer account");

        println!("\n📁 File System Access:");
        println!("  ✓ Full control over SSH directory");
        println!("  ✓ Can create/read/write SSH keys");
        println!("  ✓ Isolated from other users");

        println!("\n⚡ Next Steps:");
        println!("  1. Start the service: sc start {}", SERVICE_NAME);
        println!("  2. Check service status: sc query {}", SERVICE_NAME);
        println!("  3. View service logs in Event Viewer");
        println!("  4. Configure Windows Firewall if needed");

        println!("\n🛠️  Troubleshooting:");
        println!("  - Service logs: Event Viewer > Windows Logs > System");
        println!("  - Check permissions: icacls \"{}\"", ssh_dir.display());
        println!("  - Verify service account: sc qc {}", SERVICE_NAME);

        println!("\n{}", separator);
        println!("✅ Installation completed successfully!");
        println!("{}", separator);

        Ok(())
    }
}
