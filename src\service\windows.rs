use crate::ServiceParams;

#[cfg(target_os = "windows")]
pub async fn install_service(service_params: ServiceParams) -> anyhow::Result<()> {
    use anyhow::bail;

    let res = service::run(service_params);
    println!("Res: {:?}", res);
    if res.is_ok() {
        return Ok(());
    }
    bail!("failed to install windows service")
}

#[cfg(windows)]
mod service {
    use std::{ffi::OsString, path::Path, sync::mpsc, thread::sleep, time::Duration};

    use anyhow::Context as _;
    use windows_service::{
        define_windows_service,
        service::{
            ServiceAccess, ServiceControl, ServiceControlAccept, ServiceErrorControl, ServiceExitCode, ServiceInfo, ServiceStartType, ServiceState, ServiceStatus, ServiceType
        },
        service_control_handler::{self, ServiceControlHandlerResult},
        service_dispatcher, service_manager::{ServiceManager, ServiceManagerAccess},
    };

    use crate::ServiceParams;

    const SERVICE_NAME: &str = "iroh-ssh";
    const SERVICE_TYPE: ServiceType = ServiceType::OWN_PROCESS;
    const SERVICE_DISPLAY_NAME: &str = "iroh-ssh service";
    const SERVICE_DESCRIPTION: &str = "ssh without ip";
    const SERVICE_ACCOUNT_NAME: &str = "NT SERVICE\\iroh-ssh";

    pub fn install() -> anyhow::Result<()> {

        let profile_ssh_dir = Path::new("C:\\Windows\\ServiceProfiles\\")
            .join(SERVICE_NAME)
            .join(".ssh");

        // 2. Create the directory. `create_dir_all` is like `mkdir -p`.
        // This must be done as Administrator, which the installer will be.
        println!("Creating service data directory: {:?}", &profile_ssh_dir);
        std::fs::create_dir_all(&profile_ssh_dir)
            .with_context(|| format!("Failed to create service directory at {:?}", &profile_ssh_dir))?;

        // 3. Grant the Virtual Service Account full control over this specific directory.
        println!("Setting permissions for '{}' on {:?}", SERVICE_ACCOUNT_NAME, &profile_ssh_dir);

        // Set file system permissions for the SSH directory
        set_ssh_directory_permissions(&profile_ssh_dir, SERVICE_ACCOUNT_NAME)
            .with_context(|| format!("Failed to set permissions on {:?}", &profile_ssh_dir))?;

        // Grant network permissions for TCP listener
        grant_network_permissions(SERVICE_ACCOUNT_NAME)
            .context("Failed to grant network permissions")?;


        let manager_access = ServiceManagerAccess::CONNECT | ServiceManagerAccess::CREATE_SERVICE;
        let service_manager = ServiceManager::local_computer(None::<&str>, manager_access)?;

        // This example installs the service defined in `examples/ping_service.rs`.
        // In the real world code you would set the executable path to point to your own binary
        // that implements windows service.
        let service_binary_path = ::std::env::current_exe()
            .unwrap()
            .with_file_name("iroh-ssh.exe");

        let service_info = ServiceInfo {
            name: OsString::from(SERVICE_NAME),
            display_name: OsString::from(SERVICE_DISPLAY_NAME),
            service_type: ServiceType::OWN_PROCESS,
            start_type: ServiceStartType::AutoStart,
            error_control: ServiceErrorControl::Normal,
            executable_path: service_binary_path,
            launch_arguments: vec![OsString::from("service"), OsString::from("--ssh-port"), OsString::from("22")],
            dependencies: vec![],
            account_name: Some(OsString::from(SERVICE_ACCOUNT_NAME)), // Use Virtual Service Account
            account_password: None, // Virtual Service Accounts don't need passwords
        };
        let service =
            service_manager.create_service(&service_info, ServiceAccess::CHANGE_CONFIG)?;
        service.set_description("ssh without ip (requires ssh server)")?;
        Ok(())
    }

    pub fn run(params: ServiceParams) -> windows_service::Result<()> {
        // Register generated `ffi_service_main` with the system and start the service, blocking
        // this thread until the service is stopped.
        service_dispatcher::start(SERVICE_NAME, ffi_service_main)
    }

    // Generate the windows service boilerplate.
    // The boilerplate contains the low-level service entry function (ffi_service_main) that parses
    // incoming service arguments into Vec<OsString> and passes them to user defined service
    // entry (my_service_main).
    define_windows_service!(ffi_service_main, my_service_main);

    // Service entry function which is called on background thread by the system with service
    // parameters. There is no stdout or stderr at this point so make sure to configure the log
    // output to file if needed.
    pub fn my_service_main(_arguments: Vec<OsString>) {
        if let Err(_e) = run_service() {
            // Handle the error, by logging or something.
        }
    }

    pub fn run_service() -> windows_service::Result<()> {
        // Create a channel to be able to poll a stop event from the service worker loop.
        let (shutdown_tx, shutdown_rx) = mpsc::channel();

        // Define system service event handler that will be receiving service events.
        let event_handler = move |control_event| -> ServiceControlHandlerResult {
            match control_event {
                // Notifies a service to report its current status information to the service
                // control manager. Always return NoError even if not implemented.
                ServiceControl::Interrogate => ServiceControlHandlerResult::NoError,

                // Handle stop
                ServiceControl::Stop => {
                    shutdown_tx.send(()).unwrap();
                    ServiceControlHandlerResult::NoError
                }

                // treat the UserEvent as a stop request
                ServiceControl::UserEvent(code) => {
                    if code.to_raw() == 130 {
                        shutdown_tx.send(()).unwrap();
                    }
                    ServiceControlHandlerResult::NoError
                }

                _ => ServiceControlHandlerResult::NotImplemented,
            }
        };

        // Register system service event handler.
        // The returned status handle should be used to report service status changes to the system.
        let status_handle = service_control_handler::register(SERVICE_NAME, event_handler)?;

        // Tell the system that service is running
        status_handle.set_service_status(ServiceStatus {
            service_type: SERVICE_TYPE,
            current_state: ServiceState::Running,
            controls_accepted: ServiceControlAccept::STOP,
            exit_code: ServiceExitCode::Win32(0),
            checkpoint: 0,
            wait_hint: Duration::default(),
            process_id: None,
        })?;

        println!("in service");
        sleep(Duration::from_secs(10));
        // Poll shutdown event.
        let _ = shutdown_rx.recv();
        println!("Shutting down...");

        // Tell the system that service has stopped.
        status_handle.set_service_status(ServiceStatus {
            service_type: SERVICE_TYPE,
            current_state: ServiceState::Stopped,
            controls_accepted: ServiceControlAccept::empty(),
            exit_code: ServiceExitCode::Win32(0),
            checkpoint: 0,
            wait_hint: Duration::default(),
            process_id: None,
        })?;

        Ok(())
    }

    /// Set file system permissions for the SSH directory
    fn set_ssh_directory_permissions(ssh_dir: &Path, service_account: &str) -> anyhow::Result<()> {
        println!("Setting file permissions for service account: {}", service_account);

        // Use icacls command to grant full control to the service account
        let output = std::process::Command::new("icacls")
            .arg(ssh_dir)
            .arg("/grant")
            .arg(format!("{}:(OI)(CI)F", service_account)) // (OI)(CI)F = Object Inherit, Container Inherit, Full Control
            .arg("/T") // Apply to this folder, subfolders and files
            .output()
            .context("Failed to execute icacls command")?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            anyhow::bail!("icacls failed: {}", stderr);
        }

        println!("File permissions set successfully");
        Ok(())
    }

    /// Grant network permissions for TCP listener
    fn grant_network_permissions(service_account: &str) -> anyhow::Result<()> {
        println!("Granting network permissions for service account: {}", service_account);

        // Add the service account to the "Log on as a service" right
        // This is typically done via Local Security Policy, but we can use ntrights.exe or similar
        // For now, we'll document what needs to be done manually or via Group Policy

        println!("Network permissions configuration:");
        println!("1. The service account '{}' needs 'Log on as a service' right", service_account);
        println!("2. Windows Firewall may need to be configured to allow the application");
        println!("3. The service will run with the permissions of the Virtual Service Account");
        println!("   which should have sufficient rights to bind to local TCP ports");

        // Note: Virtual Service Accounts (NT SERVICE\\ServiceName) automatically have:
        // - Log on as a service right
        // - Network service privileges
        // - Ability to bind to local TCP ports
        // - Access to their own profile directory

        Ok(())
    }
}
